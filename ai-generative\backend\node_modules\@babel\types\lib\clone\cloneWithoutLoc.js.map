{"version": 3, "names": ["_cloneNode", "require", "cloneWithoutLoc", "node", "cloneNode"], "sources": ["../../src/clone/cloneWithoutLoc.ts"], "sourcesContent": ["import cloneNode from \"./cloneNode.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Create a shallow clone of a `node` excluding `_private` and location properties.\n */\nexport default function cloneWithoutLoc<T extends t.Node>(node: T): T {\n  return cloneNode(node, /* deep */ false, /* withoutLoc */ true);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAMe,SAASC,eAAeA,CAAmBC,IAAO,EAAK;EACpE,OAAO,IAAAC,kBAAS,EAACD,IAAI,EAAa,KAAK,EAAmB,IAAI,CAAC;AACjE", "ignoreList": []}