<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Content Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            text-align: left;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status {
            margin-top: 1rem;
            padding: 1rem;
            background: #e3f2fd;
            border-radius: 8px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Content Generator</h1>
        <p class="subtitle">Generate amazing content with AI power!</p>
        
        <form id="contentForm">
            <div class="form-group">
                <label for="topic">Topic or Keywords:</label>
                <input type="text" id="topic" placeholder="Enter your topic or keywords..." required>
            </div>
            
            <div class="form-group">
                <label for="contentType">Content Type:</label>
                <select id="contentType">
                    <option value="blog">Blog Post</option>
                    <option value="social">Social Media Post</option>
                    <option value="ad">Advertisement</option>
                    <option value="email">Email</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="tone">Tone:</label>
                <select id="tone">
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="friendly">Friendly</option>
                    <option value="formal">Formal</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="length">Length (words):</label>
                <select id="length">
                    <option value="50">Short (50 words)</option>
                    <option value="100">Medium (100 words)</option>
                    <option value="200">Long (200 words)</option>
                    <option value="500">Extended (500 words)</option>
                </select>
            </div>
            
            <button type="submit" class="btn" id="generateBtn">
                Generate Content
            </button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="status">
            <strong>🎉 Deployment Successful!</strong><br>
            Your AI Content Generator is now live on Vercel!<br>
            <small>This is a demo version. The full React app with authentication and history features will be deployed next.</small>
        </div>
    </div>

    <script>
        document.getElementById('contentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const btn = document.getElementById('generateBtn');
            const result = document.getElementById('result');
            
            // Show loading state
            btn.innerHTML = '<span class="loading"></span>Generating...';
            btn.disabled = true;
            
            // Get form values
            const topic = document.getElementById('topic').value;
            const contentType = document.getElementById('contentType').value;
            const tone = document.getElementById('tone').value;
            const length = document.getElementById('length').value;
            
            // Simulate API call (replace with actual API call later)
            setTimeout(() => {
                const mockContent = generateMockContent(topic, contentType, tone, length);
                
                result.style.display = 'block';
                result.textContent = mockContent;
                
                // Reset button
                btn.innerHTML = 'Generate Content';
                btn.disabled = false;
                
                // Scroll to result
                result.scrollIntoView({ behavior: 'smooth' });
            }, 2000);
        });
        
        function generateMockContent(topic, contentType, tone, length) {
            const templates = {
                blog: `# ${topic}: A Comprehensive Guide\n\nIn today's digital landscape, ${topic} has become increasingly important. This ${tone} guide will help you understand the key concepts and practical applications.\n\n## Key Points:\n- Understanding the fundamentals\n- Best practices and strategies\n- Real-world applications\n- Future trends and developments\n\nWhether you're a beginner or an expert, this content provides valuable insights into ${topic}.`,
                
                social: `🚀 Excited to share insights about ${topic}! \n\nHere's what you need to know:\n✅ Key benefits and advantages\n✅ Practical tips for success\n✅ Common mistakes to avoid\n\n#${topic.replace(/\s+/g, '')} #Innovation #Success`,
                
                ad: `🎯 Transform Your ${topic} Experience!\n\nDiscover the power of ${topic} with our innovative solution. Join thousands of satisfied customers who have already revolutionized their approach.\n\n✨ Key Benefits:\n• Increased efficiency\n• Better results\n• Expert support\n\n🔥 Limited Time Offer - Act Now!`,
                
                email: `Subject: Important Updates About ${topic}\n\nDear Valued Customer,\n\nWe hope this email finds you well. We're writing to share some exciting developments regarding ${topic}.\n\nOur team has been working diligently to bring you the latest innovations and improvements. Here's what's new:\n\n• Enhanced features and functionality\n• Improved user experience\n• Better performance and reliability\n\nThank you for your continued trust and support.\n\nBest regards,\nThe Team`
            };
            
            let content = templates[contentType] || templates.blog;
            
            // Adjust length
            const words = content.split(' ');
            const targetLength = parseInt(length);
            
            if (words.length > targetLength) {
                content = words.slice(0, targetLength).join(' ') + '...';
            } else if (words.length < targetLength) {
                // Add more content if needed
                content += `\n\nThis ${tone} content about ${topic} demonstrates the versatility and power of AI-generated text. The content can be customized for various purposes and audiences.`;
            }
            
            return content;
        }
    </script>
</body>
</html>
